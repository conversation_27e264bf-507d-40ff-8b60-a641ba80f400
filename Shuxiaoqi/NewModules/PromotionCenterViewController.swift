//
//  PromotionCenterViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class PromotionCenterViewController: UIViewController {
    
    // MARK: - Properties
    
    // 搜索栏
    private lazy var searchBar: UITextField = {
        let textField = UITextField()
        textField.placeholder = "搜索商品"
        textField.backgroundColor = UIColor(hex: "#F5F5F5")
        textField.layer.cornerRadius = 18
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 15, height: 36))
        textField.leftViewMode = .always
        
        // 添加搜索图标
        let searchIconView = UIImageView(image: UIImage(named: "PromotionCenter_Search"))
        searchIconView.contentMode = .center
        searchIconView.frame = CGRect(x: 0, y: 0, width: 20, height: 20)
        
        // 创建一个容器视图，使图标与右侧保持12pt的距离
        let rightViewContainer = UIView(frame: CGRect(x: 0, y: 0, width: 32, height: 36))
        searchIconView.center = CGPoint(x: rightViewContainer.bounds.width - 12 - searchIconView.bounds.width/2, 
                                        y: rightViewContainer.bounds.height/2)
        rightViewContainer.addSubview(searchIconView)
        
        textField.rightView = rightViewContainer
        textField.rightViewMode = .always
        
        return textField
    }()
    
    // 空状态视图
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        // 空状态图片
        let imageView = UIImageView()
        imageView.image = UIImage(named: "empty_data_placeholder_image")
        imageView.contentMode = .scaleAspectFit
        view.addSubview(imageView)
        
        // 空状态文字
        let label = UILabel()
        label.text = "暂时没有商品"
        label.textColor = UIColor(hex: "#999999")
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        view.addSubview(label)
        
        // 设置约束
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-30)
            make.width.height.equalTo(120)
        }
        
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
        }
        
        return view
    }()
    
    // 右侧按钮
    private lazy var rightButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("已带货商品", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.layer.cornerRadius = 15
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.cgColor
        button.addTarget(self, action: #selector(rightButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置UI
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 添加搜索栏
        view.addSubview(searchBar)
        searchBar.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(36)
        }
        
        // 添加空状态视图
        view.addSubview(emptyStateView)
        emptyStateView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(40)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    @objc private func rightButtonTapped() {
        print("点击了已带货商品按钮")
        // 这里可以添加跳转到已带货商品列表的逻辑
    }
    
    // MARK: - Public Methods
    func addRightButtonToNavigationBar() {
        // 这个方法会被主控制器调用，用于添加右侧按钮到导航栏
        if let parentVC = parent as? PromotionCenterMainViewController {
            parentVC.addCustomRightButton(rightButton)
            rightButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(90)
                make.height.equalTo(30)
            }
        }
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension PromotionCenterViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
    
    func listDidAppear() {
        print("带货中心页面显示")
        // 添加右侧按钮到导航栏
        addRightButtonToNavigationBar()
    }
    
    func listDidDisappear() {
        print("带货中心页面隐藏")
        // 移除右侧按钮
        rightButton.removeFromSuperview()
    }
}
