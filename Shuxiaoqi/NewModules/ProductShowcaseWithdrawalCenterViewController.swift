//
//  ProductShowcaseWithdrawalCenterViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/8/23.
//
//  商品橱窗-提现中心

import UIKit
import SnapKit

// MARK: - 商品橱窗提现控制器
class ProductShowcaseWithdrawalCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var availableAmount: Double = 38450.0  // 可提现金额（元）
    private var exchangeRule: String = "单笔提现金额大于100元，收益T+1到账"

    // 提现金额选项
    private var withdrawalAmounts: [ShowcaseWithdrawalAmount] = [
        ShowcaseWithdrawalAmount(yuan: 100),
        ShowcaseWithdrawalAmount(yuan: 500),
        ShowcaseWithdrawalAmount(yuan: 1000),
        ShowcaseWithdrawalAmount(yuan: 2000)
    ]
    private var selectedAmountIndex: Int = 0 // 默认选中第一个

    // 提现账户选项
    private var withdrawalAccounts: [ShowcaseWithdrawalAccount] = [
        ShowcaseWithdrawalAccount(type: .alipay, displayName: "支付宝", accountInfo: "158****1111", isLinked: true),
        ShowcaseWithdrawalAccount(type: .wechat, displayName: "微信支付", accountInfo: "未绑定", isLinked: false),
        ShowcaseWithdrawalAccount(type: .union, displayName: "银联", accountInfo: "未绑定", isLinked: false)
    ]
    private var selectedAccountIndex: Int = 0 // 默认选中支付宝

    // 变动记录
    private var transactionRecords: [ShowcaseTransactionRecord] = [
        ShowcaseTransactionRecord(type: .reward, title: "佣金奖励 迪奥烈艳蓝金唇膏", amount: 120, status: .completed, date: "2025-04-05 12:00:00"),
        ShowcaseTransactionRecord(type: .withdrawal, title: "支付宝提现", amount: -10000, status: .completed, date: "2025-04-05 12:00:00"),
        ShowcaseTransactionRecord(type: .reward, title: "佣金奖励 迪奥烈艳蓝金唇膏", amount: 2000, status: .completed, date: "2025-04-05 12:00:00")
    ]

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 可提现金额区域 - 卡片样式
    private lazy var availableAmountView: UIView = {
        let view = UIView()
        view.applyCardStyle()
        return view
    }()

    private lazy var availableAmountTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "可提现（元）"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var withdrawalInfoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现说明", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.setImage(UIImage(named: "withdrawal_info_icon"), for: .normal)
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)
        button.addTarget(self, action: #selector(withdrawalInfoButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var availableAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "¥38,450"
        label.textColor = .appThemeOrange
        label.font = .systemFont(ofSize: 32, weight: .bold)
        return label
    }()

    private lazy var exchangeRuleLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRule
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 提现账户区域 - 卡片样式
    private lazy var withdrawalAccountView: UIView = {
        let view = UIView()
        view.applyCardStyle()
        return view
    }()

    private lazy var withdrawalAccountTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "提现账户"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var accountManageButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "arrow_right_gray"), for: .normal)
        button.addTarget(self, action: #selector(accountManageButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var accountOptionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 12
        stackView.distribution = .fillEqually
        return stackView
    }()

    // 变动记录区域
    private lazy var transactionRecordsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    private lazy var transactionRecordsTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "变动记录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var viewAllRecordsButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("查看全部", for: .normal)
        button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.setImage(UIImage(named: "arrow_right_gray"), for: .normal)
        button.semanticContentAttribute = .forceRightToLeft
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: 0)
        button.addTarget(self, action: #selector(viewAllRecordsButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var recordsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fillEqually
        return stackView
    }()

    // 底部说明和按钮
    private lazy var bottomView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    private lazy var withdrawalNoticeLabel: UILabel = {
        let label = UILabel()
        label.text = "提现说明：\n1.提现金额将在1-3个工作日内到账\n2.如有疑问请联系客服\n3.平台保障资金安全，请放心提现"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 12)
        label.numberOfLines = 0
        return label
    }()

    private lazy var confirmWithdrawalButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("确认提现", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(confirmWithdrawalButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    // MARK: - 设置方法

    private func setupUI() {
        navTitle = "提现中心"
        contentView.backgroundColor = .white

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加各个区域
        scrollContentView.addSubview(availableAmountView)
        scrollContentView.addSubview(withdrawalAccountView)
        scrollContentView.addSubview(transactionRecordsView)
        scrollContentView.addSubview(bottomView)

        // 设置可提现金额区域
        availableAmountView.addSubview(availableAmountTitleLabel)
        availableAmountView.addSubview(withdrawalInfoButton)
        availableAmountView.addSubview(availableAmountLabel)
        availableAmountView.addSubview(exchangeRuleLabel)

        // 设置提现账户区域
        withdrawalAccountView.addSubview(withdrawalAccountTitleLabel)
        withdrawalAccountView.addSubview(accountManageButton)
        withdrawalAccountView.addSubview(accountOptionsStackView)

        // 设置变动记录区域
        transactionRecordsView.addSubview(transactionRecordsTitleLabel)
        transactionRecordsView.addSubview(viewAllRecordsButton)
        transactionRecordsView.addSubview(recordsStackView)

        // 设置底部区域
        bottomView.addSubview(withdrawalNoticeLabel)
        bottomView.addSubview(confirmWithdrawalButton)

        setupConstraints()
    }

    private func setupData() {
        updateAvailableAmountDisplay()
        setupAccountOptions()
        setupTransactionRecords()
    }

    private func updateAvailableAmountDisplay() {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 2
        if let formattedNumber = formatter.string(from: NSNumber(value: availableAmount)) {
            availableAmountLabel.text = "¥\(formattedNumber)"
        } else {
            availableAmountLabel.text = "¥\(availableAmount)"
        }
    }

    private func setupAccountOptions() {
        accountOptionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        for (index, account) in withdrawalAccounts.enumerated() {
            let optionView = createAccountOptionView(account: account, index: index)
            accountOptionsStackView.addArrangedSubview(optionView)
        }
    }

    private func setupTransactionRecords() {
        recordsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        for record in transactionRecords {
            let recordView = createTransactionRecordView(record: record)
            recordsStackView.addArrangedSubview(recordView)
            recordView.snp.makeConstraints { make in
                make.height.equalTo(72)
            }
        }
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 可提现金额区域约束
        availableAmountView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(116)
        }

        availableAmountTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(22)
        }

        withdrawalInfoButton.snp.makeConstraints { make in
            make.centerY.equalTo(availableAmountTitleLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(72)
            make.height.equalTo(20)
        }

        availableAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(availableAmountTitleLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
        }

        exchangeRuleLabel.snp.makeConstraints { make in
            make.top.equalTo(availableAmountLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 提现账户区域约束
        withdrawalAccountView.snp.makeConstraints { make in
            make.top.equalTo(availableAmountView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(148)
        }

        withdrawalAccountTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(22)
        }

        accountManageButton.snp.makeConstraints { make in
            make.centerY.equalTo(withdrawalAccountTitleLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(16)
        }

        accountOptionsStackView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAccountTitleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 变动记录区域约束
        transactionRecordsView.snp.makeConstraints { make in
            make.top.equalTo(withdrawalAccountView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
        }

        transactionRecordsTitleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.height.equalTo(22)
        }

        viewAllRecordsButton.snp.makeConstraints { make in
            make.centerY.equalTo(transactionRecordsTitleLabel)
            make.right.equalToSuperview()
        }

        recordsStackView.snp.makeConstraints { make in
            make.top.equalTo(transactionRecordsTitleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // 底部区域约束
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(transactionRecordsView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }

        withdrawalNoticeLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }

        confirmWithdrawalButton.snp.makeConstraints { make in
            make.top.equalTo(withdrawalNoticeLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview()
            make.height.equalTo(48)
            make.bottom.equalToSuperview()
        }
    }

    // MARK: - 创建选项视图

    private func createAccountOptionView(account: ShowcaseWithdrawalAccount, index: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1

        if index == selectedAccountIndex && account.isLinked {
            containerView.layer.borderColor = UIColor.appThemeOrange.cgColor
        } else {
            containerView.layer.borderColor = UIColor(hex: "#E5E5E5").cgColor
        }

        let iconImageView = UIImageView()
        let iconName: String
        switch account.type {
        case .alipay:
            iconName = "withdrawal_alipay_icon"
        case .wechat:
            iconName = "withdrawal_wechat_icon"
        case .union:
            iconName = "withdrawal_union_icon"
        }
        iconImageView.image = UIImage(named: iconName)
        iconImageView.contentMode = .scaleAspectFit

        let nameLabel = UILabel()
        nameLabel.text = account.displayName
        nameLabel.textColor = UIColor(hex: "#000000", alpha: 0.85)
        nameLabel.font = .systemFont(ofSize: 12, weight: .medium)

        let infoLabel = UILabel()
        infoLabel.text = account.accountInfo
        infoLabel.textColor = account.isLinked ? UIColor(hex: "#666666") : UIColor(hex: "#000000", alpha: 0.65)
        infoLabel.font = .systemFont(ofSize: 10)

        containerView.addSubview(iconImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(infoLabel)

        iconImageView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().offset(10)
            make.width.height.equalTo(20)
        }

        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.left)
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.height.equalTo(13)
        }

        infoLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.right.equalTo(nameLabel)
            make.bottom.equalToSuperview().offset(-10)
        }

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(accountOptionTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.tag = index

        if !account.isLinked {
            containerView.alpha = 0.6
        }

        return containerView
    }

    private func createTransactionRecordView(record: ShowcaseTransactionRecord) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white

        let titleLabel = UILabel()
        titleLabel.text = record.title
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)

        let dateLabel = UILabel()
        dateLabel.text = formatDate(record.date)
        dateLabel.textColor = UIColor(hex: "#999999")
        dateLabel.font = .systemFont(ofSize: 12)

        let amountLabel = UILabel()
        let amountText: String
        let amountColor: UIColor
        
        if record.amount > 0 {
            amountText = "+¥\(Int(record.amount))"
            amountColor = UIColor(hex: "#FF6B6B")
        } else {
            amountText = "-¥\(Int(-record.amount))"
            amountColor = UIColor(hex: "#4CAF50")
        }
        
        amountLabel.text = amountText
        amountLabel.textColor = amountColor
        amountLabel.font = .systemFont(ofSize: 14, weight: .medium)
        amountLabel.textAlignment = .right

        let statusLabel = UILabel()
        statusLabel.text = record.status.displayText
        statusLabel.textColor = UIColor(hex: "#999999")
        statusLabel.font = .systemFont(ofSize: 12)
        statusLabel.textAlignment = .right

        containerView.addSubview(titleLabel)
        containerView.addSubview(dateLabel)
        containerView.addSubview(amountLabel)
        containerView.addSubview(statusLabel)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(16)
        }

        dateLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-16)
        }

        amountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalTo(titleLabel)
        }

        statusLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalTo(dateLabel)
        }

        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#F5F5F5")
        containerView.addSubview(separatorLine)

        separatorLine.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        return containerView
    }

    // MARK: - 辅助方法

    private func formatDate(_ dateString: String) -> String {
        let components = dateString.components(separatedBy: " ")
        if components.count >= 2 {
            return components[0] + " " + components[1]
        }
        return dateString
    }

    private func updateAccountSelection(_ newIndex: Int) {
        selectedAccountIndex = newIndex
        setupAccountOptions()
    }

    // MARK: - 事件处理
    @objc private func withdrawalInfoButtonTapped() {
        print("提现说明按钮被点击")
        let alert = UIAlertController(title: "提现说明", message: "商品橱窗提现相关规则和说明", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func accountManageButtonTapped() {
        print("账户管理按钮被点击")
        let alert = UIAlertController(title: "账户管理", message: "跳转到账户管理页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func viewAllRecordsButtonTapped() {
        print("查看全部记录按钮被点击")
        let alert = UIAlertController(title: "全部记录", message: "跳转到交易记录页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func accountOptionTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        let account = withdrawalAccounts[index]

        if account.isLinked {
            if index != selectedAccountIndex {
                updateAccountSelection(index)
                print("选择提现账户：\(account.displayName)")
            }
        } else {
            print("跳转到\(account.displayName)绑定页面")
            showAccountBindingPage(for: account.type)
        }
    }

    private func showAccountBindingPage(for accountType: ShowcaseWithdrawalAccountType) {
        var accountTypeName: String
        switch accountType {
        case .alipay:
            accountTypeName = "支付宝"
        case .wechat:
            accountTypeName = "微信支付"
        case .union:
            accountTypeName = "银联"
        }
        
        let alert = UIAlertController(title: "绑定\(accountTypeName)", message: "跳转到\(accountTypeName)绑定页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func confirmWithdrawalButtonTapped() {
        print("确认提现按钮被点击")

        let selectedAccount = withdrawalAccounts[selectedAccountIndex]

        if !selectedAccount.isLinked {
            let alert = UIAlertController(title: "提示", message: "请先绑定提现账户", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        if availableAmount < 100 {
            let alert = UIAlertController(title: "提示", message: "提现金额不能少于100元", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        let message = "确认提现全部金额¥\(availableAmount)到\(selectedAccount.displayName)(\(selectedAccount.accountInfo))？"
        let alert = UIAlertController(title: "确认提现", message: message, preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "确认", style: .default) { [weak self] _ in
            self?.performWithdrawal()
        })

        present(alert, animated: true)
    }

    private func performWithdrawal() {
        let withdrawalAmount = availableAmount
        availableAmount = 0
        updateAvailableAmountDisplay()

        let newRecord = ShowcaseTransactionRecord(
            type: .withdrawal,
            title: "\(withdrawalAccounts[selectedAccountIndex].displayName)提现",
            amount: -withdrawalAmount,
            status: .processing,
            date: getCurrentDateString()
        )
        transactionRecords.insert(newRecord, at: 0)
        setupTransactionRecords()

        let alert = UIAlertController(title: "提现申请成功", message: "您的提现申请已提交，预计1-3个工作日内到账", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func getCurrentDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: Date())
    }
}

// MARK: - 数据模型

/// 橱窗提现金额选项
struct ShowcaseWithdrawalAmount {
    let yuan: Int        // 人民币金额
}

/// 橱窗提现账户类型
enum ShowcaseWithdrawalAccountType {
    case alipay  // 支付宝
    case wechat  // 微信支付
    case union   // 银联
}

/// 橱窗提现账户
struct ShowcaseWithdrawalAccount {
    let type: ShowcaseWithdrawalAccountType
    let displayName: String    // 显示名称
    let accountInfo: String    // 账户信息
    let isLinked: Bool        // 是否已绑定
}

/// 橱窗交易记录类型
enum ShowcaseTransactionType {
    case reward      // 奖励
    case withdrawal  // 提现
}

/// 橱窗交易状态
enum ShowcaseTransactionStatus {
    case completed   // 已完成
    case processing  // 处理中
    case failed      // 失败

    var displayText: String {
        switch self {
        case .completed:
            return "已到账"
        case .processing:
            return "处理中"
        case .failed:
            return "失败"
        }
    }
}

/// 橱窗交易记录
struct ShowcaseTransactionRecord {
    let type: ShowcaseTransactionType
    let title: String           // 标题
    let amount: Double         // 金额（正数为收入，负数为支出）
    let status: ShowcaseTransactionStatus
    let date: String           // 日期
}
