//
//  MyPromotionViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class MyPromotionViewController: UIViewController {
    
    // MARK: - Properties
    
    // 用户信息卡片
    private lazy var userInfoCardView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8
        
        // 头像
        let avatarImageView = UIImageView()
        avatarImageView.image = UIImage(named: "default_avatar")
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.layer.cornerRadius = 30
        avatarImageView.clipsToBounds = true
        avatarImageView.backgroundColor = UIColor(hex: "#F0F0F0")
        view.addSubview(avatarImageView)
        
        // 用户名
        let nameLabel = UILabel()
        nameLabel.text = "带货达人"
        nameLabel.textColor = UIColor(hex: "#333333")
        nameLabel.font = UIFont.boldSystemFont(ofSize: 18)
        view.addSubview(nameLabel)
        
        // 等级标签
        let levelLabel = UILabel()
        levelLabel.text = "LV.5 高级带货师"
        levelLabel.textColor = UIColor(hex: "#8E44AD")
        levelLabel.font = UIFont.systemFont(ofSize: 14)
        view.addSubview(levelLabel)
        
        // 设置约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(60)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(15)
            make.top.equalTo(avatarImageView.snp.top).offset(8)
        }
        
        levelLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
        }
        
        // 设置卡片高度
        view.snp.makeConstraints { make in
            make.height.equalTo(100)
        }
        
        return view
    }()
    
    // 数据统计视图
    private lazy var statsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8
        
        // 创建统计项
        let stats = [
            ("累计收益", "¥5,678.90"),
            ("带货商品", "156"),
            ("粉丝数量", "2.3K")
        ]
        
        for (index, stat) in stats.enumerated() {
            let containerView = UIView()
            view.addSubview(containerView)
            
            let titleLabel = UILabel()
            titleLabel.text = stat.0
            titleLabel.textColor = UIColor(hex: "#666666")
            titleLabel.font = UIFont.systemFont(ofSize: 14)
            titleLabel.textAlignment = .center
            containerView.addSubview(titleLabel)
            
            let valueLabel = UILabel()
            valueLabel.text = stat.1
            valueLabel.textColor = UIColor(hex: "#8E44AD")
            valueLabel.font = UIFont.boldSystemFont(ofSize: 20)
            valueLabel.textAlignment = .center
            containerView.addSubview(valueLabel)
            
            // 设置约束
            titleLabel.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(20)
                make.centerX.equalToSuperview()
            }
            
            valueLabel.snp.makeConstraints { make in
                make.top.equalTo(titleLabel.snp.bottom).offset(8)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-20)
            }
            
            // 设置容器约束
            containerView.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.width.equalToSuperview().dividedBy(3)
                make.left.equalToSuperview().offset(CGFloat(index) * UIScreen.main.bounds.width / 3 - 32)
            }
            
            // 添加分隔线（除了最后一个）
            if index < stats.count - 1 {
                let separatorLine = UIView()
                separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
                view.addSubview(separatorLine)
                separatorLine.snp.makeConstraints { make in
                    make.right.equalTo(containerView)
                    make.centerY.equalToSuperview()
                    make.width.equalTo(1)
                    make.height.equalTo(40)
                }
            }
        }
        
        return view
    }()
    
    // 我的商品列表
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(MyPromotionTableViewCell.self, forCellReuseIdentifier: "MyPromotionTableViewCell")
        return tableView
    }()
    
    // 示例数据
    private let myProducts = [
        ["title": "春季新款连衣裙", "status": "热销中", "sales": "156", "commission": "¥23.40"],
        ["title": "护肤精华液", "status": "已下架", "sales": "89", "commission": "¥15.60"],
        ["title": "运动鞋", "status": "热销中", "sales": "234", "commission": "¥45.80"]
    ]
    
    // 右侧按钮
    private lazy var rightButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("编辑", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.layer.cornerRadius = 15
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.cgColor
        button.addTarget(self, action: #selector(rightButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = UIColor(hex: "#F5F7FA")
        
        // 设置UI
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 添加用户信息卡片
        view.addSubview(userInfoCardView)
        userInfoCardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // 添加数据统计视图
        view.addSubview(statsView)
        statsView.snp.makeConstraints { make in
            make.top.equalTo(userInfoCardView.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(80)
        }
        
        // 添加表格视图
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(statsView.snp.bottom).offset(16)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    @objc private func rightButtonTapped() {
        print("点击了编辑按钮")
    }
    
    // MARK: - Public Methods
    func addRightButtonToNavigationBar() {
        if let parentVC = parent as? PromotionCenterMainViewController {
            parentVC.addCustomRightButton(rightButton)
            rightButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(60)
                make.height.equalTo(30)
            }
        }
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension MyPromotionViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
    
    func listDidAppear() {
        print("我的带货页面显示")
        addRightButtonToNavigationBar()
    }
    
    func listDidDisappear() {
        print("我的带货页面隐藏")
        rightButton.removeFromSuperview()
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension MyPromotionViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return myProducts.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MyPromotionTableViewCell", for: indexPath) as! MyPromotionTableViewCell
        cell.configure(with: myProducts[indexPath.row])
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        print("选择了商品：\(myProducts[indexPath.row]["title"] ?? "")")
    }
}

// MARK: - MyPromotionTableViewCell
class MyPromotionTableViewCell: UITableViewCell {
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 1)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 2
        return view
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    private let statusLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = .white
        label.textAlignment = .center
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        return label
    }()
    
    private let salesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#666666")
        return label
    }()
    
    private let commissionLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#8E44AD")
        label.textAlignment = .right
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(statusLabel)
        containerView.addSubview(salesLabel)
        containerView.addSubview(commissionLabel)
        
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(statusLabel.snp.left).offset(-10)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(60)
            make.height.equalTo(20)
        }
        
        salesLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.left.equalToSuperview().offset(16)
        }
        
        commissionLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    func configure(with product: [String: String]) {
        titleLabel.text = product["title"]
        salesLabel.text = "销量: \(product["sales"] ?? "")"
        commissionLabel.text = product["commission"]
        
        if let status = product["status"] {
            statusLabel.text = status
            statusLabel.backgroundColor = status == "热销中" ? UIColor(hex: "#4CAF50") : UIColor(hex: "#FF9800")
        }
    }
}
