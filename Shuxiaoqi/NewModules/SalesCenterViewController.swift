//
//  SalesCenterViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class SalesCenterViewController: UIViewController {
    
    // MARK: - Properties
    
    // 统计卡片视图
    private lazy var statsCardView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8
        
        // 今日销售额标题
        let titleLabel = UILabel()
        titleLabel.text = "今日销售额"
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.systemFont(ofSize: 16)
        view.addSubview(titleLabel)
        
        // 销售额数字
        let amountLabel = UILabel()
        amountLabel.text = "¥1,234.56"
        amountLabel.textColor = UIColor(hex: "#007AFF")
        amountLabel.font = UIFont.boldSystemFont(ofSize: 28)
        view.addSubview(amountLabel)
        
        // 订单数量
        let orderLabel = UILabel()
        orderLabel.text = "订单数量: 23"
        orderLabel.textColor = UIColor(hex: "#666666")
        orderLabel.font = UIFont.systemFont(ofSize: 14)
        view.addSubview(orderLabel)
        
        // 设置约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(20)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.left.equalToSuperview().offset(20)
        }
        
        orderLabel.snp.makeConstraints { make in
            make.top.equalTo(amountLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        return view
    }()
    
    // 功能按钮容器
    private lazy var functionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8
        
        // 创建功能按钮
        let functions = [
            ("订单管理", "order_management"),
            ("数据分析", "data_analysis"),
            ("商品管理", "product_management"),
            ("客户管理", "customer_management")
        ]
        
        var lastButton: UIButton?
        
        for (index, function) in functions.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(function.0, for: .normal)
            button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.backgroundColor = UIColor(hex: "#F8F9FA")
            button.layer.cornerRadius = 8
            button.tag = index
            button.addTarget(self, action: #selector(functionButtonTapped(_:)), for: .touchUpInside)
            
            view.addSubview(button)
            
            if index < 2 {
                // 第一行
                button.snp.makeConstraints { make in
                    make.top.equalToSuperview().offset(20)
                    make.height.equalTo(44)
                    make.width.equalTo(120)
                    if index == 0 {
                        make.left.equalToSuperview().offset(20)
                    } else {
                        make.right.equalToSuperview().offset(-20)
                    }
                }
            } else {
                // 第二行
                button.snp.makeConstraints { make in
                    make.top.equalToSuperview().offset(84)
                    make.height.equalTo(44)
                    make.width.equalTo(120)
                    make.bottom.equalToSuperview().offset(-20)
                    if index == 2 {
                        make.left.equalToSuperview().offset(20)
                    } else {
                        make.right.equalToSuperview().offset(-20)
                    }
                }
            }
        }
        
        return view
    }()
    
    // 右侧按钮
    private lazy var rightButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("设置", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.layer.cornerRadius = 15
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.cgColor
        button.addTarget(self, action: #selector(rightButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = UIColor(hex: "#F5F7FA")
        
        // 设置UI
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 添加统计卡片
        view.addSubview(statsCardView)
        statsCardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // 添加功能按钮容器
        view.addSubview(functionsView)
        functionsView.snp.makeConstraints { make in
            make.top.equalTo(statsCardView.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Actions
    @objc private func functionButtonTapped(_ sender: UIButton) {
        let functions = ["订单管理", "数据分析", "商品管理", "客户管理"]
        print("点击了\(functions[sender.tag])按钮")
    }
    
    @objc private func rightButtonTapped() {
        print("点击了设置按钮")
    }
    
    // MARK: - Public Methods
    func addRightButtonToNavigationBar() {
        if let parentVC = parent as? PromotionCenterMainViewController {
            parentVC.addCustomRightButton(rightButton)
            rightButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(60)
                make.height.equalTo(30)
            }
        }
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension SalesCenterViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
    
    func listDidAppear() {
        print("销售中心页面显示")
        addRightButtonToNavigationBar()
    }
    
    func listDidDisappear() {
        print("销售中心页面隐藏")
        rightButton.removeFromSuperview()
    }
}
