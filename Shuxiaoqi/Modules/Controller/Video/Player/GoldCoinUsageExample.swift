//
//  GoldCoinUsageExample.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/11.
//

import UIKit
import SnapKit

/// 金币控件使用示例
/// 展示如何在不同场景下使用GoldCoinView
class GoldCoinUsageExample: UIViewController {
    
    // MARK: - 示例1：首页使用（无动画）
    private let homePageGoldCoin = GoldCoinView()
    
    // MARK: - 示例2：视频页面使用（有动画）
    private let videoPageGoldCoin = GoldCoinView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupExamples()
    }
    
    private func setupUI() {
        view.backgroundColor = .black
        title = "金币控件使用示例"
        
        // 添加说明标签
        let titleLabel = UILabel()
        titleLabel.text = "金币控件使用示例"
        titleLabel.textColor = .white
        titleLabel.font = .boldSystemFont(ofSize: 24)
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(20)
            make.centerX.equalToSuperview()
        }
        
        // 首页示例说明
        let homeLabel = UILabel()
        homeLabel.text = "首页使用（无动画）"
        homeLabel.textColor = .white
        homeLabel.font = .systemFont(ofSize: 16)
        view.addSubview(homeLabel)
        
        homeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(40)
            make.left.equalToSuperview().offset(20)
        }
        
        // 视频页面示例说明
        let videoLabel = UILabel()
        videoLabel.text = "视频页面使用（有动画）"
        videoLabel.textColor = .white
        videoLabel.font = .systemFont(ofSize: 16)
        view.addSubview(videoLabel)
        
        videoLabel.snp.makeConstraints { make in
            make.top.equalTo(homeLabel.snp.bottom).offset(100)
            make.left.equalToSuperview().offset(20)
        }
        
        // 添加金币控件
        view.addSubview(homePageGoldCoin)
        view.addSubview(videoPageGoldCoin)
        
        // 首页金币控件布局
        homePageGoldCoin.snp.makeConstraints { make in
            make.size.equalTo(40)
            make.top.equalTo(homeLabel.snp.bottom).offset(20)
            make.trailing.equalToSuperview().offset(-20)
        }
        
        // 视频页面金币控件布局
        videoPageGoldCoin.snp.makeConstraints { make in
            make.size.equalTo(40)
            make.top.equalTo(videoLabel.snp.bottom).offset(20)
            make.trailing.equalToSuperview().offset(-20)
        }
    }
    
    private func setupExamples() {
        // 示例1：首页使用（无动画）
        setupHomePageExample()
        
        // 示例2：视频页面使用（有动画）
        setupVideoPageExample()
    }
    
    private func setupHomePageExample() {
        // 首页使用：隐藏进度条，只显示金币图标
        homePageGoldCoin.setProgressVisible(false)
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(homePageGoldCoinTapped))
        homePageGoldCoin.addGestureRecognizer(tapGesture)
        homePageGoldCoin.isUserInteractionEnabled = true
    }
    
    private func setupVideoPageExample() {
        // 视频页面使用：全局水桶机制
        let isLoggedIn = AuthManager.shared.isLoggedIn

        videoPageGoldCoin.setupGoldCoin(isUserLoggedIn: isLoggedIn)

        // 设置金币奖励回调
        videoPageGoldCoin.onGoldCoinEarned = { [weak self] in
            self?.showGoldCoinEarnedAlert()
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(videoPageGoldCoinTapped))
        videoPageGoldCoin.addGestureRecognizer(tapGesture)
        videoPageGoldCoin.isUserInteractionEnabled = true

        // 如果已登录，模拟开始播放
        if isLoggedIn {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.videoPageGoldCoin.startPlaying()
            }
        }
    }
    
    @objc private func homePageGoldCoinTapped() {
        let alert = UIAlertController(
            title: "首页金币",
            message: "这是首页的金币控件，点击可以查看金币相关信息",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func videoPageGoldCoinTapped() {
        if AuthManager.shared.isLoggedIn {
            let alert = UIAlertController(
                title: "视频页面金币",
                message: "这是视频页面的金币控件，观看60秒可获得金币奖励",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        } else {
            let alert = UIAlertController(
                title: "需要登录",
                message: "请先登录以使用金币功能",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
    
    private func showGoldCoinEarnedAlert() {
        DispatchQueue.main.async {
            let alert = UIAlertController(
                title: "恭喜！",
                message: "您获得了金币奖励！",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "太棒了", style: .default))
            self.present(alert, animated: true)
        }
    }
}

// MARK: - 使用说明
/*
 
 ## 金币控件使用指南
 
 ### 1. 首页使用（无动画）
 ```swift
 let goldCoin = GoldCoinView()
 goldCoin.setProgressVisible(false) // 隐藏进度条
 view.addSubview(goldCoin)
 
 goldCoin.snp.makeConstraints { make in
     make.size.equalTo(40)
     make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(140)
     make.trailing.equalToSuperview().offset(-20)
 }
 ```
 
 ### 2. 视频页面使用（全局水桶机制）
 ```swift
 let goldCoin = GoldCoinView()
 goldCoin.setupGoldCoin(isUserLoggedIn: AuthManager.shared.isLoggedIn)

 // 设置奖励回调
 goldCoin.onGoldCoinEarned = {
     // 处理金币奖励（60秒满了就倒掉）
 }

 // 播放控制
 goldCoin.startPlaying()  // 开始播放
 goldCoin.pausePlaying()  // 暂停播放
 goldCoin.seekTo(time: 30.0)  // 拖动进度条
 ```

 ### 3. 登录状态区别
 - **未登录**: 2秒转一圈，保留圆圈，点击跳转登录
 - **已登录**: 全局60秒水桶机制，满了就倒掉并请求API

 ### 4. 全局水桶机制
 - 所有视频的观看时长累计到一个全局桶中
 - 切换视频时保持当前进度（如10秒+下个视频=继续从10/60秒开始）
 - 60秒满了就倒掉（请求API+重置时长）
 - 进度条显示：当前全局累计时长/60秒

 ### 5. 本地记录
 - 使用沙盒记录全局观看时间
 - 支持跨会话保存观看进度
 - 水桶满了后自动重置
 
 */
