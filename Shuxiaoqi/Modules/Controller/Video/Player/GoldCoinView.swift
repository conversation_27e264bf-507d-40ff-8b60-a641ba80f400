//
//  GoldCoinView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/11.
//

import UIKit
import SnapKit

// MARK: - 本地存储管理器（全局水桶机制）
class GoldCoinStorageManager {
    static let shared = GoldCoinStorageManager()
    private let userDefaults = UserDefaults.standard
    private let globalWatchTimeKey = "GoldCoin_GlobalWatchTime"

    private init() {}

    /// 获取全局累计观看时间
    func getGlobalWatchTime() -> TimeInterval {
        return userDefaults.double(forKey: globalWatchTimeKey)
    }

    /// 保存全局累计观看时间
    func saveGlobalWatchTime(_ time: TimeInterval) {
        userDefaults.set(time, forKey: globalWatchTimeKey)
        print("[GoldCoinStorage] 保存全局观看时间: \(time)s")
    }

    /// 重置全局观看时间（获得金币后）
    func resetGlobalWatchTime() {
        userDefaults.set(0.0, forKey: globalWatchTimeKey)
        print("[GoldCoinStorage] 重置全局观看时间")
    }

    /// 增加观看时间
    func addWatchTime(_ deltaTime: TimeInterval) -> TimeInterval {
        let currentTime = getGlobalWatchTime()
        let newTime = currentTime + deltaTime
        saveGlobalWatchTime(newTime)
        return newTime
    }

    /// 清除所有记录（测试用）
    func clearAllRecords() {
        userDefaults.removeObject(forKey: globalWatchTimeKey)
    }
}

/// 金币控件
/// 40*40大小，透明黑灰色圆形背景，中间30*30金币图片，外围#E9D65A色进度条
/// 支持转圈动画（2秒一圈）和外部控制显示/隐藏进度条
class GoldCoinView: UIView {
    
    // MARK: - UI Components
    private let backgroundView = UIView()
    private let coinImageView = UIImageView()
    private let progressLayer = CAShapeLayer()
    private var rotationAnimation: CABasicAnimation?
    
    // MARK: - Properties
    private var isAnimating = false
    private var shouldShowProgress = true
    private var isLoggedIn = false
    private var isPlaying = false
    private var lastUpdateTime: Date?
    private var goldCoinTimer: Timer?

    // 全局水桶机制
    private var globalWatchTime: TimeInterval = 0

    // 回调
    var onGoldCoinEarned: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 设置背景圆形视图
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        backgroundView.layer.cornerRadius = 20 // 40*40的一半
        addSubview(backgroundView)
        
        // 设置金币图片
        coinImageView.image = UIImage(named: "video_gold_coin")
        coinImageView.contentMode = .scaleAspectFit
        addSubview(coinImageView)
        
        // 设置进度条
        setupProgressLayer()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupProgressLayer() {
        // 创建圆形路径
        let center = CGPoint(x: 20, y: 20) // 40*40的中心点
        let radius: CGFloat = 19 // 半径稍小于背景圆形
        let startAngle = -CGFloat.pi / 2 // 从顶部开始
        let endAngle = startAngle + 2 * CGFloat.pi // 完整圆形
        
        let circularPath = UIBezierPath(arcCenter: center,
                                       radius: radius,
                                       startAngle: startAngle,
                                       endAngle: endAngle,
                                       clockwise: true)
        
        progressLayer.path = circularPath.cgPath
        progressLayer.strokeColor = UIColor(hex: "#E9D65A").cgColor
        progressLayer.lineWidth = 2.0
        progressLayer.fillColor = UIColor.clear.cgColor
        progressLayer.lineCap = .round
        progressLayer.strokeStart = 0
        progressLayer.strokeEnd = 0
        
        layer.addSublayer(progressLayer)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(30)
        }
    }
    
    // MARK: - Public Methods

    /// 设置登录状态
    func setupGoldCoin(isUserLoggedIn: Bool) {
        self.isLoggedIn = isUserLoggedIn

        // 停止之前的计时器
        stopGoldCoinTimer()

        if isLoggedIn {
            // 已登录：从本地获取全局观看时间并更新显示
            globalWatchTime = GoldCoinStorageManager.shared.getGlobalWatchTime()
            updateProgressDisplay()
        } else {
            // 未登录：简单的2秒动画
            startUnloggedAnimation()
        }

        print("[GoldCoin] 设置金币控件 - 已登录: \(isUserLoggedIn), 全局观看时间: \(globalWatchTime)s")
    }

    /// 开始播放（已登录用户开始计时）
    func startPlaying() {
        guard isLoggedIn else { return }

        isPlaying = true
        lastUpdateTime = Date()
        startGoldCoinTimer()

        print("[GoldCoin] 开始播放计时")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isLoggedIn else { return }

        isPlaying = false
        updateGlobalWatchTime()
        stopGoldCoinTimer()

        print("[GoldCoin] 暂停播放，全局观看时间: \(globalWatchTime)s")
    }

    /// 拖动进度条时调用（不影响全局时长，只是暂停/恢复计时）
    func seekTo(time: TimeInterval) {
        guard isLoggedIn else { return }

        // 拖动时更新全局时长
        if isPlaying {
            updateGlobalWatchTime()
            lastUpdateTime = Date()
        }

        print("[GoldCoin] 拖动进度条，全局观看时间: \(globalWatchTime)s")
    }

    /// 未登录用户的简单动画
    private func startUnloggedAnimation() {
        guard shouldShowProgress else { return }

        progressLayer.removeAllAnimations()
        progressLayer.strokeEnd = 0
        progressLayer.isHidden = false
        isAnimating = true

        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = 0
        animation.toValue = 1
        animation.duration = 2.0
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.delegate = self

        progressLayer.add(animation, forKey: "unloggedAnimation")
        progressLayer.strokeEnd = 1
    }

    /// 开始金币计时器（已登录用户）
    private func startGoldCoinTimer() {
        stopGoldCoinTimer()

        goldCoinTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateGoldCoinProgress()
        }
    }

    /// 停止金币计时器
    private func stopGoldCoinTimer() {
        goldCoinTimer?.invalidate()
        goldCoinTimer = nil
    }

    /// 更新金币进度（每0.1秒调用一次）
    private func updateGoldCoinProgress() {
        guard isLoggedIn, isPlaying, let lastTime = lastUpdateTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        // 更新全局观看时间
        globalWatchTime += deltaTime
        lastUpdateTime = now

        // 保存到本地
        GoldCoinStorageManager.shared.saveGlobalWatchTime(globalWatchTime)

        // 更新进度条显示
        updateProgressDisplay()

        // 检查是否完成60秒周期（水桶满了）
        if globalWatchTime >= 60.0 {
            // 完成一个60秒周期，触发金币奖励
            onGoldCoinEarned?()

            // 重置全局时间（倒掉水桶）
            globalWatchTime = 0
            GoldCoinStorageManager.shared.resetGlobalWatchTime()

            // 更新进度条显示
            updateProgressDisplay()

            print("[GoldCoin] 完成60秒周期，触发金币奖励，重置全局时间")
        }
    }

    /// 更新进度条显示
    private func updateProgressDisplay() {
        guard isLoggedIn else { return }

        // 计算当前60秒周期内的进度
        let cycleProgress = globalWatchTime / 60.0

        progressLayer.isHidden = false
        progressLayer.strokeEnd = CGFloat(cycleProgress)

        print("[GoldCoin] 更新进度: \(cycleProgress * 100)%，全局时间: \(globalWatchTime)s")
    }

    /// 更新全局观看时间
    private func updateGlobalWatchTime() {
        guard let lastTime = lastUpdateTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        globalWatchTime += deltaTime
        lastUpdateTime = now

        // 保存到本地
        GoldCoinStorageManager.shared.saveGlobalWatchTime(globalWatchTime)

        // 更新进度条显示
        updateProgressDisplay()
    }

    /// 设置是否显示进度条（用于首页等无动画场景）
    func setProgressVisible(_ visible: Bool) {
        shouldShowProgress = visible
        if !visible {
            stopGoldCoinTimer()
            progressLayer.isHidden = true
        }
    }

    /// 设置金币图片
    func setCoinImage(_ image: UIImage?) {
        coinImageView.image = image
    }

    /// 获取当前全局观看时间（用于调试）
    func getCurrentGlobalWatchTime() -> TimeInterval {
        return globalWatchTime
    }

    /// 获取当前进度百分比（用于调试）
    func getCurrentProgress() -> Double {
        return globalWatchTime / 60.0
    }

    /// 手动添加观看时间（用于测试）
    func addTestWatchTime(_ seconds: TimeInterval) {
        guard isLoggedIn else { return }

        globalWatchTime += seconds
        GoldCoinStorageManager.shared.saveGlobalWatchTime(globalWatchTime)
        updateProgressDisplay()

        // 检查是否满60秒
        if globalWatchTime >= 60.0 {
            onGoldCoinEarned?()
            globalWatchTime = 0
            GoldCoinStorageManager.shared.resetGlobalWatchTime()
            updateProgressDisplay()
        }

        print("[GoldCoin] 测试添加 \(seconds)s，当前全局时间: \(globalWatchTime)s")
    }

    /// 停止所有动画和计时器
    func stopAll() {
        stopGoldCoinTimer()
        progressLayer.removeAllAnimations()
        isAnimating = false
        isPlaying = false

        if isLoggedIn {
            updateGlobalWatchTime()
        }
    }
}

// MARK: - CAAnimationDelegate
extension GoldCoinView: CAAnimationDelegate {
    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        if flag {
            isAnimating = false
            if !isLoggedIn {
                // 未登录用户：动画完成后保持进度条填满状态
                progressLayer.strokeEnd = 1
            }
        }
    }
}
