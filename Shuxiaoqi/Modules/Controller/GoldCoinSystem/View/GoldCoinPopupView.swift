//
//  GoldCoinPopupView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/19.
//

import UIKit
import SnapKit

// MARK: - 金币弹窗视图（左右24pt，按327:436动态高度）
class GoldCoinPopupView: UIView {
    enum Mode { case countdown, reward }
    // 遮罩
    private let dimView: UIControl = {
        let v = UIControl()
        v.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        return v
    }()

    // 背景卡片（切图）
    private let contentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "countdown_window_obtaining_gold_coins")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = true
        return iv
    }()

    // 叠加容器：与 contentImageView 同尺寸，用于按比例放置内部控件
    let contentOverlayView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()

    // 文案：标题（“倒计时结束后/恭喜获得”）
    private let titleLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#000000", alpha: 0.85)
        lb.numberOfLines = 1
        return lb
    }()

    // 文案：大号数值（倒计时/金币）
    private let valueLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF6666")
        lb.numberOfLines = 1
        return lb
    }()

    // 比例占位视图（用于把 top/间距/高度按 436 设计高度折算）
    private let topSpacer = UIView()
    private let gapSpacer = UIView()
    private let bottomSpacer = UIView()
    

    private var mode: Mode = .countdown
    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF8F1F")
        lb.numberOfLines = 1
        return lb
    }()
    private let subtitleGap4 = UIView() // 标题与副标题 4pt 间距

    // 确认按钮
    private let confirmButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("确认", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        btn.backgroundColor = UIColor(hex: "#FF8F1F")
        btn.layer.cornerRadius = 21
        btn.clipsToBounds = true
        return btn
    }()
    private var confirmBottomConstraint: Constraint?
    var onConfirm: (() -> Void)?

    // 关闭按钮
    private let closeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "countdown_window_obtaining_gold_coins_closed_btn"), for: .normal)
        return btn
    }()

    // 对外回调
    var onDismiss: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .clear

        addSubview(dimView)
        addSubview(contentImageView)
        addSubview(closeButton)

        dimView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 内容卡片：左右各24pt，按宽高比 327:436 计算高度（整体上移24pt）
        contentImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(24)
            make.centerY.equalToSuperview().offset(-24)
            make.height.equalTo(contentImageView.snp.width).multipliedBy(436.0/327.0)
        }

        // 叠加容器与卡片同大小
        contentImageView.addSubview(contentOverlayView)
        contentOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        setupOverlayContents()
        // 确认按钮放在内容区域底部，底部间距按比例缩放
        contentOverlayView.addSubview(confirmButton)
        confirmButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(262)
            make.height.equalTo(43)
            confirmBottomConstraint = make.bottom.equalToSuperview().offset(-36).constraint
        }
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        // 让 bottomSpacer 的底部贴到按钮顶部，从而使 valueLabel 在上下内容间居中
        bottomSpacer.snp.makeConstraints { make in
            make.bottom.equalTo(confirmButton.snp.top)
        }

        // 关闭按钮居中，位于卡片下方
        let closeSize: CGFloat = (closeButton.image(for: .normal)?.size.width ?? 28)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(contentImageView.snp.bottom).offset(16)
            make.centerX.equalTo(contentImageView.snp.centerX)
            make.width.height.equalTo(closeSize)
            make.bottom.lessThanOrEqualTo(self.safeAreaLayoutGuide.snp.bottom).offset(-20)
        }

        dimView.addTarget(self, action: #selector(handleBackgroundTap), for: .touchUpInside)
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
    }

    // MARK: - Public
    func show(in parent: UIView) {
        parent.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 动画
        alpha = 0
        contentImageView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        UIView.animate(withDuration: 0.22, delay: 0, options: [.curveEaseOut]) {
            self.alpha = 1
            self.contentImageView.transform = .identity
        }
    }

    func dismiss(animated: Bool = true) {
        let animations = {
            self.alpha = 0
            self.contentImageView.transform = CGAffineTransform(scaleX: 0.92, y: 0.92)
        }
        let completion: (Bool) -> Void = { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        }
        if animated {
            UIView.animate(withDuration: 0.18, animations: animations, completion: completion)
        } else {
            animations()
            completion(true)
        }
    }

    // 配置文案
    func configure(title: String, subtitle: String) {
        titleLabel.text = title
        subtitleLabel.text = subtitle
        adjustFontsForScale()
    }

    // 固定字号，已与设计匹配；无需按比例缩放
    private func adjustFontsForScale() {
        titleLabel.font = .systemFont(ofSize: 24, weight: .medium)
        subtitleLabel.font = .systemFont(ofSize: 16)
        valueLabel.font = .systemFont(ofSize: 48, weight: .semibold)
    }

    // MARK: - Actions
    @objc private func handleBackgroundTap() {
        dismiss(animated: true)
    }

    @objc private func closeTapped() {
        dismiss(animated: true)
    }
    
    @objc private func confirmTapped() {
        onConfirm?()
        dismiss(animated: true)
    }

    // MARK: - Overlay contents and layout
    private func setupOverlayContents() {
        // 使用垂直排列：topSpacer(比例) -> title(35) -> subtitleGap(4) -> subtitle(17) -> gap(40) -> value(58) -> bottom(自适应)
        let stack = UIStackView(arrangedSubviews: [topSpacer, titleLabel, subtitleGap4, subtitleLabel, gapSpacer, valueLabel, bottomSpacer])
        stack.axis = .vertical
        stack.alignment = .fill
        stack.distribution = .fill
        stack.spacing = 0
        contentOverlayView.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
        }

        // 比例高度约束
        topSpacer.snp.makeConstraints { make in
            make.height.equalTo(contentOverlayView.snp.height).multipliedBy(169.0/436.0)
        }
        titleLabel.snp.makeConstraints { make in
            make.height.equalTo(35)
        }
        subtitleGap4.snp.makeConstraints { make in
            make.height.equalTo(4)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.height.equalTo(17)
        }
        gapSpacer.snp.makeConstraints { make in
            make.height.equalTo(bottomSpacer.snp.height)
        }
        valueLabel.snp.makeConstraints { make in
            make.height.equalTo(58)
        }
        // bottomSpacer 高度由剩余空间决定，无需固定比例，但可给最小高度保证
        bottomSpacer.setContentHuggingPriority(.defaultLow, for: .vertical)
        bottomSpacer.setContentCompressionResistancePriority(.defaultLow, for: .vertical)

        // 默认文案
        titleLabel.font = .systemFont(ofSize: 24, weight: .medium)
        valueLabel.font = .systemFont(ofSize: 48, weight: .semibold)
        subtitleLabel.font = .systemFont(ofSize: 16)
        configure(mode: .countdown, valueText: "00:00:30", subtitleText: "即可获得200金币！")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        // 按需刷新一次，保持按钮底部固定 36pt
        confirmBottomConstraint?.update(offset: -36.0)
    }

    // MARK: - Public configure
    func configure(mode: Mode, valueText: String, subtitleText: String? = nil) {
        self.mode = mode
        switch mode {
        case .countdown:
            titleLabel.text = "倒计时结束后"
            subtitleLabel.text = subtitleText
            let hideSubtitle = (subtitleText?.isEmpty ?? true)
            subtitleLabel.isHidden = hideSubtitle
            subtitleGap4.isHidden = hideSubtitle
            confirmButton.setTitle("确认", for: .normal)
        case .reward:
            titleLabel.text = "恭喜获得"
            subtitleLabel.text = nil
            subtitleLabel.isHidden = true
            subtitleGap4.isHidden = true
            confirmButton.setTitle("开心收下", for: .normal)
        }
        valueLabel.text = valueText
        setNeedsLayout()
    }
}
