//
//  TaskRowView.swift
//  Shuxiaoqi
//
//  Created by yong<PERSON>ng ye on 2025/8/19.
//

import UIKit

// MARK: - 任务行组件（无卡片样式）
class TaskRowView: UIView {

    // MARK: - UI 组件
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
//        imageView.contentMode = .scaleAspectFit
//        imageView.backgroundColor = UIColor(hex: "#F0F0F0")
//        imageView.layer.cornerRadius = 20
//        imageView.clipsToBounds = true
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#000000",alpha: 0.45)
        label.font = .systemFont(ofSize: 12)
        return label
    }()

    private lazy var rewardLabel: UILabel = {
        let label = UILabel()
        label.textColor = .appThemeOrange
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 4
        button.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 属性
    var onButtonTapped: (() -> Void)?
    private var isCompleted: Bool = false

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .white

        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
        addSubview(rewardLabel)
        addSubview(actionButton)

        setupConstraints()
    }

    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.top.equalTo(iconImageView.snp.top).offset(-4)
            make.height.equalTo(23)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.height.equalTo(17)
            // 移除底部间距，让容器自己控制间距
        }

        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(subtitleLabel) // 按钮对齐副标题
            make.width.equalTo(60)
            make.height.equalTo(23)
        }

        rewardLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(titleLabel) // +金币数对齐标题
        }
    }

    // MARK: - 公共方法

    func configure(icon: UIImage?, title: String, subtitle: String, reward: String, buttonTitle: String, isCompleted: Bool) {
        iconImageView.image = icon
        titleLabel.text = title
        subtitleLabel.text = subtitle
        rewardLabel.text = reward
        actionButton.setTitle(buttonTitle, for: .normal)
        setCompleted(isCompleted)
    }

    func setCompleted(_ completed: Bool) {
        isCompleted = completed
        if completed {
            actionButton.backgroundColor = UIColor(hex: "#CCCCCC")
            actionButton.setTitle("已完成", for: .normal)
            actionButton.isEnabled = false
        } else {
            actionButton.backgroundColor = .appThemeOrange
            actionButton.isEnabled = true
        }
    }

    func updateButtonTitle(_ title: String) {
        actionButton.setTitle(title, for: .normal)
    }

    @objc private func actionButtonTapped() {
        if !isCompleted {
            onButtonTapped?()
        }
    }
}
