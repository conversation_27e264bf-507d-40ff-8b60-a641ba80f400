//
//  DraggableGiftButton.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/19.
//

import UIKit

// MARK: - 悬浮礼盒按钮（可长按拖动）
class DraggableGiftButton: UIControl {
    // 背景包含“礼盒 + 橙色条”的整图
    private let backgroundImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_gift_package")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = false
        return iv
    }()

    // 仅管理橙色区域的文案
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 10, weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 1
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.8
        return label
    }()

    // 拖拽
    private var startTouchPointInSelf: CGPoint = .zero
    private var didDrag: Bool = false
    private let dragEdgeInset: CGFloat = 8
    var edgeSnapEnabled: Bool = true

    override init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    private func setup() {
        clipsToBounds = false
        isUserInteractionEnabled = true
        isExclusiveTouch = false

        addSubview(backgroundImageView)
        addSubview(titleLabel)

        // 长按开始拖动
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPress.minimumPressDuration = 0.25
        longPress.cancelsTouchesInView = true
        addGestureRecognizer(longPress)

        // 轻点触发点击事件
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        addGestureRecognizer(tap)
    }

    // 文案设置（作用于橙色区域）
    func setTitle(_ title: String) {
        titleLabel.text = title
        setNeedsLayout()
    }

    // 按标题动态计算合适的宽度（包含橙色条左右内边距）
    // maxWidth 由外部限制，避免过宽溢出屏幕
    func preferredWidth(maxWidth: CGFloat) -> CGFloat {
        let text = titleLabel.text ?? ""
        let font = titleLabel.font ?? .systemFont(ofSize: 10, weight: .medium)
        // 文本单行宽度估算
        let bounding = (text as NSString).boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 28),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        )
        // 与 layoutSubviews 中保持一致的左右内边距
        let horizontalInsets: CGFloat = 14 + 14
        // 为文本增加少量余量，避免挤压
        let padding: CGFloat = 10
        let wanted = bounding.width + horizontalInsets + padding
        // 设定一个适度的最小宽度，避免图片过度压缩
        let minWidth: CGFloat = 120
        // 若有原始图片尺寸，尽量不超过其 1.6 倍，避免过度拉伸
        let intrinsicW = intrinsicContentSize.width
        let safeMax = intrinsicW.isFinite && intrinsicW > 0 ? min(maxWidth, intrinsicW * 1.6) : maxWidth
        return max(minWidth, min(wanted, safeMax))
    }

    override var intrinsicContentSize: CGSize {
        if let size = backgroundImageView.image?.size, size.width > 0, size.height > 0 {
            return size
        }
        return CGSize(width: 156, height: 120)
    }

    override func sizeThatFits(_ size: CGSize) -> CGSize {
        return intrinsicContentSize
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        backgroundImageView.frame = bounds

        // 将文案放到背景图底部的橙色区域
        let barHeight = max(32, bounds.height * 0.26)
        let insets = UIEdgeInsets(top: 0, left: 14, bottom: 10, right: 14)
        let labelY = bounds.height - barHeight
        let labelFrame = CGRect(x: insets.left,
                                y: labelY + (barHeight - 28) * 0.5 + 3,
                                width: bounds.width - insets.left - insets.right,
                                height: 28)
        titleLabel.frame = labelFrame
    }

    // MARK: - 拖拽手势
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        guard let superview = self.superview else { return }
        switch gesture.state {
        case .began:
            didDrag = false
            startTouchPointInSelf = gesture.location(in: self)
            animate(scale: 1.03, alpha: 1)
        case .changed:
            let locationInSuper = gesture.location(in: superview)
            var newCenter = CGPoint(x: locationInSuper.x - startTouchPointInSelf.x + bounds.width * 0.5,
                                    y: locationInSuper.y - startTouchPointInSelf.y + bounds.height * 0.5)
            let halfW = bounds.width * 0.5
            let halfH = bounds.height * 0.5
            let minX = dragEdgeInset + halfW
            let maxX = superview.bounds.width - dragEdgeInset - halfW
            let minY = dragEdgeInset + halfH
            let maxY = superview.bounds.height - dragEdgeInset - halfH
            newCenter.x = max(minX, min(maxX, newCenter.x))
            newCenter.y = max(minY, min(maxY, newCenter.y))
            if distance(from: center, to: newCenter) > 0.5 { didDrag = true }
            center = newCenter
        case .ended, .cancelled, .failed:
            if edgeSnapEnabled, let superview = self.superview {
                let leftX = dragEdgeInset + bounds.width * 0.5
                let rightX = superview.bounds.width - dragEdgeInset - bounds.width * 0.5
                let targetX = (center.x < superview.bounds.midX) ? leftX : rightX
                UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseOut]) {
                    self.center.x = targetX
                }
            }
            animate(scale: 1.0, alpha: 1)
        default:
            break
        }
    }

    @objc private func handleTap() {
        // 触发 UIControl 的 .touchUpInside 事件
        sendActions(for: .touchUpInside)
    }

    private func distance(from: CGPoint, to: CGPoint) -> CGFloat {
        let dx = from.x - to.x
        let dy = from.y - to.y
        return sqrt(dx*dx + dy*dy)
    }

    private func animate(scale: CGFloat, alpha: CGFloat) {
        UIView.animate(withDuration: 0.15) {
            self.transform = CGAffineTransform(scaleX: scale, y: scale)
            self.alpha = alpha
        }
    }
}
