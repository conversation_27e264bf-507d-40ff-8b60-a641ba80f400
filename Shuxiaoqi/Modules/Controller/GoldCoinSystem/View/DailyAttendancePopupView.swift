//
//  DailyAttendancePopupView.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/8/19.
//

import UIKit

// MARK: - 日签弹窗（337:446 比例，左右各 19pt）
class DailyAttendancePopupView: UIView {
    // 遮罩
    private let dimView: UIControl = {
        let v = UIControl()
        v.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        return v
    }()

    // 背景图（按 337:446 比例缩放，不变形）
    private let contentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_bg")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = true
        return iv
    }()

    // 关闭按钮（位于弹窗上方 14pt，屏幕右侧 -14pt，29x29）
    private let closeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "gold_coin_‌daily_attendance‌_cancel"), for: .normal)
        return btn
    }()

    // 基于 337 宽度的等比缩放系数
    private var scaleFactor: CGFloat = 1.0

    // 标题
    private let titleLabel: UILabel = {
        let lb = UILabel()
        lb.textColor = UIColor(hex: "#502202")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 23, weight: .semibold)
        return lb
    }()

    // 副标题
    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.textColor = UIColor(hex: "#BD7611")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 11)
        return lb
    }()

    // 内容标题：“今日签到可领”（#FF8400，12pt，高36pt，居中）
    private let contentTitleLabel: UILabel = {
        let lb = UILabel()
        lb.text = "今日签到可领"
        lb.textColor = UIColor(hex: "#FF8400")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        return lb
    }()

    // 顶部图标 gold_coin_‌daily_attendance‌_cell_ordinary（58x31）
    private let topIconImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_cell_ordinary")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 金币展示标签（高度43，颜色#FF8400，数字31pt，单位12pt）
    private let coinAmountLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF8400")
        lb.numberOfLines = 1
        return lb
    }()
    private var todayCoinAmount: Int = 6666

    // 进度容器（白色圆角背景，贴底，高 80s）
    private let progressContainerView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.clipsToBounds = true
        return v
    }()
    // 背景装饰线（#FFE6B0，高 4s）
    private let progressDecorLine: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#FFE6B0")
        return v
    }()
    private let progressStackView: UIStackView = {
        let st = UIStackView()
        st.axis = .horizontal
        st.alignment = .fill
        st.distribution = .fillEqually
        st.spacing = 8
        return st
    }()
    // 图标尺寸约束缓存，便于在 layoutSubviews 时按可用宽度缩放
    private var progressIconWidthConstraints: [NSLayoutConstraint] = []
    private var progressIconHeightConstraints: [NSLayoutConstraint] = []

    // 主内容图
    private let mainContentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_content")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 立即签到按钮（资源自带文案）
    private let confirmButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setBackgroundImage(UIImage(named: "gold_coin_‌daily_attendance‌_ok_btn"), for: .normal)
        return btn
    }()

    // 对外回调
    var onDismiss: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .clear

        addSubview(dimView)
        addSubview(contentImageView)
        addSubview(closeButton)

        dimView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 内容卡片：左右各19pt，高度 = 宽度 * (446/337)，垂直居中
        contentImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(19)
            make.centerY.equalToSuperview()
            make.height.equalTo(contentImageView.snp.width).multipliedBy(446.0/337.0)
        }

        // 关闭按钮：右侧距屏幕 14pt，按钮底部到卡片顶部 14pt，尺寸 29x29
        closeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-14)
            make.width.height.equalTo(29)
            make.bottom.equalTo(contentImageView.snp.top).offset(-14)
        }

        // 计算等比缩放系数（以 337 为基准）
        let contentWidth = UIScreen.main.bounds.width - 38
        scaleFactor = contentWidth / 337.0

        // 添加内容视图到卡片内（先放入主内容背景，保证其处于最底层；再放标题/副标题等）
        contentImageView.addSubview(mainContentImageView)
        contentImageView.addSubview(titleLabel)
        contentImageView.addSubview(subtitleLabel)
        mainContentImageView.addSubview(contentTitleLabel)
        mainContentImageView.addSubview(topIconImageView)
        mainContentImageView.addSubview(coinAmountLabel)
        contentImageView.addSubview(confirmButton)

        // 标题：顶部 29s，高 32s
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(29.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.height.equalTo(32.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 副标题：紧贴标题下方，高 15s
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.centerX.equalToSuperview()
            make.height.equalTo(15.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 内容标题：高36s，贴合主内容背景顶部
        contentTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(mainContentImageView.snp.top)
            make.centerX.equalToSuperview()
            make.height.equalTo(36.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 顶部图标：距内容标题 23s，58x31（乘 s）
        topIconImageView.snp.makeConstraints { make in
            make.top.equalTo(contentTitleLabel.snp.bottom).offset(23.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.width.equalTo(58.0 * scaleFactor)
            make.height.equalTo(31.0 * scaleFactor)
        }

        // 金币展示：贴紧图标底部，高 43s
        coinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(topIconImageView.snp.bottom) // 贴紧
            make.centerX.equalToSuperview()
            make.height.equalTo(43.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 主内容图：距弹窗背景图顶部 95s；尺寸固定 308x231（乘 s）
        mainContentImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(95.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.width.equalTo(308.0 * scaleFactor)
            make.height.equalTo(231.0 * scaleFactor)
        }

        // 金币展示与主内容背景的关系已由固定主内容位置保证，无需额外 bottom 约束

        // 进度容器：贴住主内容背景底部，高 80s，宽度与主内容背景相等
        mainContentImageView.addSubview(progressContainerView)
        progressContainerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(80.0) // 固定80pt高度
        }
        // 圆角随比例
        progressContainerView.layer.cornerRadius = 12.0 * scaleFactor

        // 进度栈（先添加，避免装饰线约束时与之无共同父视图导致崩溃）
        progressContainerView.addSubview(progressStackView)
        progressStackView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(8.0)
            make.top.equalToSuperview().offset(8.0 * scaleFactor)
            make.bottom.equalToSuperview().offset(-8.0 * scaleFactor)
        }

        // 装饰线在栈视图下方（背部）
        progressContainerView.insertSubview(progressDecorLine, belowSubview: progressStackView)
        progressDecorLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(8.0)
            make.height.equalTo(4.0 * scaleFactor)
            // 装饰线底部到容器底部 49pt（等价于：装饰线下方距离“第N天”上方18pt）
            make.bottom.equalToSuperview().offset(-49.0)
        }

        // 立即签到按钮：距主内容图 31s，尺寸 183x52（乘 s）。
        // 增加按钮到底部的约束，防止整体内容把背景图向下“挤”动；并降低顶部间距的优先级以便小屏自适应。
        confirmButton.snp.makeConstraints { make in
            make.top.equalTo(mainContentImageView.snp.bottom).offset(31.0 * scaleFactor).priority(750)
            make.centerX.equalToSuperview()
            make.width.equalTo(183.0 * scaleFactor)
            make.height.equalTo(52.0 * scaleFactor)
            make.bottom.lessThanOrEqualToSuperview().offset(-24.0 * scaleFactor)
        }

        dimView.addTarget(self, action: #selector(handleBackgroundTap), for: .touchUpInside)
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)

        // 初次根据高度调整字体
        adjustFontsForScale()
        // 设置默认金币展示
        updateCoinAmountLabel()
    }

    // MARK: - Public
    /// 配置标题与副标题
    func configure(title: String, subtitle: String) {
        titleLabel.text = title
        subtitleLabel.text = subtitle
        adjustFontsForScale()
    }

    /// 根据缩放系数微调字体：在小屏时略微缩小，正常及大屏保持设计字号
    private func adjustFontsForScale() {
        // 设计基准字号
        let baseTitle: CGFloat = 23.0
        let baseSubtitle: CGFloat = 11.0
        let baseContentTitle: CGFloat = 12.0
        // 仅在 scaleFactor < 1 时按比例缩小，且给出最小保护
        let titleSize = scaleFactor < 1.0 ? max(12.0, baseTitle * scaleFactor) : baseTitle
        let subtitleSize = scaleFactor < 1.0 ? max(9.0, baseSubtitle * scaleFactor) : baseSubtitle
        titleLabel.font = .systemFont(ofSize: titleSize, weight: .semibold)
        subtitleLabel.font = .systemFont(ofSize: subtitleSize)
        let contentTitleSize = scaleFactor < 1.0 ? max(10.0, baseContentTitle * scaleFactor) : baseContentTitle
        contentTitleLabel.font = .systemFont(ofSize: contentTitleSize, weight: .medium)
    }

    /// 对外设置今日金币数量
    func setCoinAmount(_ amount: Int) {
        todayCoinAmount = max(0, amount)
        updateCoinAmountLabel()
    }

    private func updateCoinAmountLabel() {
        // 数字 31pt，单位 12pt；颜色统一 #FF8400，高度 43s
        let numberText = "\(todayCoinAmount)"
        let unitText = "金币"
        let full = numberText + unitText
        let attr = NSMutableAttributedString(string: full)
        let color = UIColor(hex: "#FF8400")
        let scale = min(1.0, scaleFactor)
        let numberFont = UIFont.systemFont(ofSize: 31.0 * scale, weight: .semibold)
        let unitFont = UIFont.systemFont(ofSize: 12.0 * scale)
        attr.addAttributes([.foregroundColor: color, .font: numberFont], range: NSRange(location: 0, length: numberText.count))
        attr.addAttributes([.foregroundColor: color, .font: unitFont], range: NSRange(location: numberText.count, length: unitText.count))
        coinAmountLabel.attributedText = attr
    }

    /// 构建或更新签到进度
    func setProgress(currentIndex: Int, amounts: [Int]) {
        // 清空旧内容
        progressStackView.arrangedSubviews.forEach { view in
            progressStackView.removeArrangedSubview(view)
            view.removeFromSuperview()
        }
        progressIconWidthConstraints.removeAll()
        progressIconHeightConstraints.removeAll()
        // 规范化索引与缩放
        let idx = max(0, min(6, currentIndex))
        let s = min(1.0, scaleFactor)

        for i in 0..<7 {
            let item = UIView()
            progressStackView.addArrangedSubview(item)

            // 图标
            let icon = UIImageView()
            icon.contentMode = .scaleAspectFit
            if i == 6 {
                icon.image = UIImage(named: "gold_coin_‌daily_attendance‌_cell_rewards")
            } else {
                icon.image = UIImage(named: "gold_coin_‌daily_attendance‌_content_cell_ordinary")
            }

            // 金币数量（白色，10pt，中等）
            let coinLabel = UILabel()
            coinLabel.textAlignment = .center
            coinLabel.textColor = .white
            coinLabel.font = .systemFont(ofSize: 10.0, weight: .medium)
            if i < amounts.count { coinLabel.text = "\(amounts[i])" } else { coinLabel.text = "" }

            // 天数/今天 标签
            let dayLabel = UILabel()
            dayLabel.text = (i == idx) ? "今天" : "第\(i + 1)天"
            dayLabel.textColor = (i == idx) ? UIColor(hex: "#FF8400") : UIColor(hex: "#333333")
            dayLabel.textAlignment = .center
            dayLabel.font = .systemFont(ofSize: 12.0 * s)

            // 添加到 item，并做自下而上的精确约束
            // 图层顺序：装饰线(容器外) < 图标(含橙色胶囊) < 金币金额文字
            item.addSubview(icon)
            item.addSubview(coinLabel)
            item.addSubview(dayLabel)

            // 第n天文案（或“今天”）：距离容器底部16pt，高度15pt
            dayLabel.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-14.0)
                make.height.equalTo(15.0)
            }
            // 基准尺寸用于确定图标宽高比（宽=cell宽，高按比例）
            let baseW: CGFloat = 39.0
            let baseH: CGFloat = (i == 6 ? 38.0 : 35.0)
            // 图标：在“第N天”文案上方 1pt；宽度=cell宽度；高度按比例自适应，上方自由增加
            icon.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.bottom.equalTo(dayLabel.snp.top).offset(-1.0)
                make.height.equalTo(icon.snp.width).multipliedBy(baseH / baseW)
            }
            // 金币金额文案：贴在图标内部靠下，避免与“第N天”重叠（距图标底部向上3pt）
            coinLabel.snp.makeConstraints { make in
                make.centerX.equalTo(icon.snp.centerX)
                make.bottom.equalTo(icon.snp.bottom).offset(-3.0)
            }

            // 注意：不再使用固定宽高常量，改为“宽=cell宽度，高=按比例”。
        }

        // 触发布局以应用最新尺寸
        setNeedsLayout()
        layoutIfNeeded()
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        updateProgressIconSizes()
    }

    private func updateProgressIconSizes() {
        // 计算每个 item 可用宽度和高度（考虑间距与文字占位）
        let totalWidth = progressStackView.bounds.width
        let totalHeight = progressStackView.bounds.height
        let count = CGFloat(progressStackView.arrangedSubviews.count)
        guard totalWidth > 0, totalHeight > 0, count > 0 else { return }
        let totalSpacing = progressStackView.spacing * max(0, count - 1)
        let itemWidth = max(0, (totalWidth - totalSpacing) / count)

        // 文字行高估算（与 setProgress 中字体保持一致）
        let fontScale = min(1.0, scaleFactor)
        let dayLineHeight = UIFont.systemFont(ofSize: 12.0 * fontScale).lineHeight
        let coinLineHeight = UIFont.systemFont(ofSize: 10.0 * fontScale, weight: .medium).lineHeight
        let verticalSpacings = (4.0 + 6.0) * scaleFactor
        let maxIconHeight = max(0, totalHeight - dayLineHeight - coinLineHeight - verticalSpacings)

        let baseIconW = 39.0 * scaleFactor
        // 宽高方向都参与缩放，取最小比例；且不放大
        let widthScale = min(1.0, itemWidth / baseIconW)

        for (idx, wConstraint) in progressIconWidthConstraints.enumerated() {
            let baseH: CGFloat = (idx == 6 ? 38.0 : 35.0) * scaleFactor
            let heightScale = baseH > 0 ? min(1.0, maxIconHeight / baseH) : 1.0
            let scale = min(widthScale, heightScale)
            wConstraint.constant = baseIconW * scale
            if idx < progressIconHeightConstraints.count {
                progressIconHeightConstraints[idx].constant = baseH * scale
            }
        }
    }

    func show(in parent: UIView) {
        parent.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 动画
        alpha = 0
        contentImageView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        UIView.animate(withDuration: 0.22, delay: 0, options: [.curveEaseOut]) {
            self.alpha = 1
            self.contentImageView.transform = .identity
        }
    }

    func dismiss(animated: Bool = true) {
        let animations = {
            self.alpha = 0
            self.contentImageView.transform = CGAffineTransform(scaleX: 0.92, y: 0.92)
        }
        let completion: (Bool) -> Void = { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        }
        if animated {
            UIView.animate(withDuration: 0.18, animations: animations, completion: completion)
        } else {
            animations()
            completion(true)
        }
    }

    // MARK: - Actions
    @objc private func handleBackgroundTap() {
        dismiss(animated: true)
    }

    @objc private func closeTapped() {
        dismiss(animated: true)
    }

    // 供后续接入确认签到逻辑
    var onConfirm: (() -> Void)?
    @objc private func confirmTapped() {
        onConfirm?()
    }
}
