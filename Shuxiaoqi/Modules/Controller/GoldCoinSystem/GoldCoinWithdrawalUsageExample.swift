//
//  GoldCoinWithdrawalUsageExample.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/13.
//
//  金币提现页面使用示例

import UIKit

// MARK: - 金币提现页面使用示例
class GoldCoinWithdrawalUsageExample {
    
    /// 从任务中心跳转到提现页面
    static func showWithdrawalFromTaskCenter(from viewController: UIViewController) {
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        
        // 可以在这里设置一些初始数据
        // withdrawalVC.setInitialGoldCoins(8888)
        
        viewController.navigationController?.pushViewController(withdrawalVC, animated: true)
    }
    
    /// 从其他页面跳转到提现页面
    static func showWithdrawal(from viewController: UIViewController, goldCoins: Int = 8888) {
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        
        // 如果需要设置特定的金币数量，可以添加相应的方法
        // withdrawalVC.setInitialGoldCoins(goldCoins)
        
        viewController.navigationController?.pushViewController(withdrawalVC, animated: true)
    }
    
    /// 模态方式展示提现页面
    static func presentWithdrawal(from viewController: UIViewController, goldCoins: Int = 8888) {
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        let navController = UINavigationController(rootViewController: withdrawalVC)
        
        viewController.present(navController, animated: true)
    }
}

// MARK: - 在现有的任务中心中添加提现跳转
// 注意：由于 withdrawButtonTapped 是 private 方法，无法在扩展中重写
// 提现功能已经在 GoldCoinSystemTaskCenterViewController 中直接实现

// MARK: - 使用示例说明
/*
 
 ## 金币提现页面功能说明
 
 ### 主要功能
 1. **显示可提现金币数量**：当前用户的可提现金币余额
 2. **提现金额选择**：提供多个预设金额选项（¥50、¥100、¥200、¥500）
 3. **提现账户管理**：支持支付宝和微信支付账户
 4. **交易记录查看**：显示最近的金币变动记录
 5. **提现申请**：完整的提现流程和状态管理
 
 ### 设计特点
 - 遵循设计稿的UI布局和颜色规范
 - 支持选中状态的视觉反馈
 - 完整的交互逻辑和错误处理
 - 模块化的数据模型设计
 
 ### 使用方法
 ```swift
 // 1. 从任务中心跳转
 GoldCoinWithdrawalUsageExample.showWithdrawalFromTaskCenter(from: self)
 
 // 2. 从其他页面跳转
 GoldCoinWithdrawalUsageExample.showWithdrawal(from: self, goldCoins: 10000)
 
 // 3. 模态展示
 GoldCoinWithdrawalUsageExample.presentWithdrawal(from: self)
 ```
 
 ### 需要的图片资源
 - `withdrawal_info_icon`: 提现说明图标
 - `arrow_right_gray`: 右箭头图标
 - `withdrawal_alipay_icon`: 支付宝图标
 - `withdrawal_wechat_icon`: 微信支付图标
 - `nav_menu_dots`: 导航栏菜单图标
 
 ### 后续扩展
 1. 添加自定义金额输入功能
 2. 集成真实的支付接口
 3. 添加提现手续费计算
 4. 支持更多提现方式
 5. 添加提现限额和频次控制
 
 */
