//
//  GoldCoinSystemWithdrawalAccountManagementViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/15.
//

import UIKit
import SnapKit

/// 提现账号管理类型
enum WithdrawalAccountManagementType {
    case goldCoin      // 金币系统
    case productShowcase // 商品橱窗（带货）
}

/// 金币系统 - 提现账号管理
class GoldCoinSystemWithdrawalAccountManagementViewController: BaseViewController {
    
    // MARK: - Properties
    
    /// 管理类型，默认为金币系统
    var managementType: WithdrawalAccountManagementType = .goldCoin
    
    // MARK: - 状态
    
    /// 支付宝是否已绑定
    var alipayBound: Bool = false {
        didSet { updateAccountStates() }
    }
    /// 支付宝账号展示（打码）
    var alipayMaskedAccount: String? { didSet { updateAccountStates() } }
    
    /// 微信是否已绑定
    var wechatBound: Bool = false {
        didSet { updateAccountStates() }
    }
    /// 微信账号展示（打码）
    var wechatMaskedAccount: String? { didSet { updateAccountStates() } }
    
    /// 银联是否已绑定
    var unionPayBound: Bool = false {
        didSet { updateAccountStates() }
    }
    /// 银联账号展示（打码）
    var unionPayMaskedAccount: String? { didSet { updateAccountStates() } }
    
    // MARK: - UI
    
    private lazy var alipayCard = WithdrawalAccountCardView(
        iconImageName: "withdrawal_alipay_icon",
        title: "支付宝"
    )
    
    private lazy var wechatCard = WithdrawalAccountCardView(
        iconImageName: "withdrawal_wechat_icon",
        title: "微信支付"
    )
    
    private lazy var unionPayCard = WithdrawalAccountCardView(
        iconImageName: "withdrawal_unionpay_icon",
        title: "银联"
    )
    
    private let tipsContainer: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#FFF6ED")
        v.layer.cornerRadius = 8
        return v
    }()
    
    private let tipsIconImageView: UIImageView = {
        let iv = UIImageView(image: UIImage(named: "gold_coin_reminder"))
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    
    private let tipsTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "温馨提示"
        l.textColor = UIColor(hex: "#FF8F1F")
        l.font = UIFont.boldSystemFont(ofSize: 14)
        return l
    }()
    
    private let tipsBodyLabel: UILabel = {
        let l = UILabel()
        l.textColor = UIColor(hex: "#827D79")
        l.font = UIFont.systemFont(ofSize: 12)
        l.numberOfLines = 0
        l.text = "• 为确保资金安全，绑定账号后需要 48 小时后才能提现\n• 请确保绑定本人实名认证的收款账号\n• 如遇问题请联系客服：400-888-8888"
        return l
    }()
    
    private let safetySupportLabel: UILabel = {
        let l = UILabel()
        l.text = "账号安全由平台提供技术支持"
        l.textColor = UIColor(hex: "#BBBBBB")
        l.font = UIFont.systemFont(ofSize: 12)
        l.textAlignment = .center
        return l
    }()
    
    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "提现账号管理"
        setupUI()
        bindActions()
        updateAccountStates()
    }
    
    /// 便利初始化方法
    convenience init(type: WithdrawalAccountManagementType) {
        self.init()
        self.managementType = type
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .white
        contentView.backgroundColor = .white
        
        // 卡片
        contentView.addSubview(alipayCard)
        contentView.addSubview(wechatCard)
        
        // 根据类型决定是否添加银联卡片
        if managementType == .productShowcase {
            contentView.addSubview(unionPayCard)
        }
        
        // 提示
        contentView.addSubview(tipsContainer)
        tipsContainer.addSubview(tipsIconImageView)
        tipsContainer.addSubview(tipsTitleLabel)
        tipsContainer.addSubview(tipsBodyLabel)
        contentView.addSubview(safetySupportLabel)
        
        bringNavBarToFront()
        
        // 布局
        alipayCard.snp.makeConstraints { make in
            make.top.equalTo(contentView).offset(16)
            make.left.right.equalTo(contentView).inset(16)
        }
        
        wechatCard.snp.makeConstraints { make in
            make.top.equalTo(alipayCard.snp.bottom).offset(12)
            make.left.right.equalTo(alipayCard)
        }
        
        // 根据类型决定银联卡片的约束
        if managementType == .productShowcase {
            unionPayCard.snp.makeConstraints { make in
                make.top.equalTo(wechatCard.snp.bottom).offset(12)
                make.left.right.equalTo(alipayCard)
            }
            
            tipsContainer.snp.makeConstraints { make in
                make.top.equalTo(unionPayCard.snp.bottom).offset(24)
                make.left.right.equalTo(alipayCard)
                make.height.equalTo(104)
            }
        } else {
            tipsContainer.snp.makeConstraints { make in
                make.top.equalTo(wechatCard.snp.bottom).offset(24)
                make.left.right.equalTo(alipayCard)
                make.height.equalTo(104)
            }
        }
        
        tipsIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(10)
            make.size.equalTo(16)
        }
        
        tipsTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.equalTo(tipsIconImageView.snp.right).offset(8)
        }
        tipsBodyLabel.snp.makeConstraints { make in
            make.top.equalTo(tipsTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(tipsIconImageView.snp.left)
            make.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().inset(12)
        }
        
        safetySupportLabel.snp.makeConstraints { make in
            make.top.equalTo(tipsContainer.snp.bottom).offset(12)
            make.left.right.equalTo(alipayCard)
        }
        
        applyTipsText()
    }
    
    private func bindActions() {
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(alipayTapped))
        alipayCard.addGestureRecognizer(tap1)
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(wechatTapped))
        wechatCard.addGestureRecognizer(tap2)
        
        // 根据类型决定是否绑定银联点击事件
        if managementType == .productShowcase {
            let tap3 = UITapGestureRecognizer(target: self, action: #selector(unionPayTapped))
            unionPayCard.addGestureRecognizer(tap3)
        }
    }
    
    private func updateAccountStates() {
        alipayCard.subtitle = alipayBound ? (alipayMaskedAccount ?? "已绑定") : "未绑定"
        wechatCard.subtitle = wechatBound ? (wechatMaskedAccount ?? "已绑定") : "未绑定"
        
        // 根据类型更新银联状态
        if managementType == .productShowcase {
            unionPayCard.subtitle = unionPayBound ? (unionPayMaskedAccount ?? "已绑定") : "未绑定"
        }
    }
    
    /// 设置提示文案的行距等样式
    private func applyTipsText() {
        let text = "• 为确保资金安全，绑定账号后需要 48 小时后才能提现\n• 请确保绑定本人实名认证的收款账号\n• 如遇问题请联系客服：400-888-8888"
        let paragraph = NSMutableParagraphStyle()
        paragraph.lineSpacing = 4
        paragraph.alignment = .left
        let attr = NSAttributedString(
            string: text,
            attributes: [
                .font: UIFont.systemFont(ofSize: 12),
                .foregroundColor: UIColor(hex: "#827D79"),
                .paragraphStyle: paragraph
            ]
        )
        tipsBodyLabel.attributedText = attr
    }
    
    /// 简单账号打码：
    /// - 邮箱：a***<EMAIL>
    /// - 手机或其它：保留前3后3，中间打码
    private func maskAccount(_ s: String) -> String {
        let trimmed = s.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return "已绑定" }
        if let at = trimmed.firstIndex(of: "@") {
            let name = String(trimmed[..<at])
            let domain = String(trimmed[at...])
            if name.count <= 2 { return "*" + domain }
            let first = name.first!
            let last = name.last!
            return "\(first)***\(last)" + domain
        } else {
            if trimmed.count <= 6 { return "***" }
            let start = trimmed.prefix(3)
            let end = trimmed.suffix(3)
            return "\(start)****\(end)"
        }
    }
    
    // MARK: - Actions
    @objc private func alipayTapped() {
        // 弹出支付宝实名认证弹窗
        let popup = WithdrawalRealnamePopupView(channel: .alipay)
        popup.onConfirm = { [weak self] input in
            guard let self = self else { return }
            self.alipayBound = true
            if let acc = input.account, !acc.isEmpty {
                self.alipayMaskedAccount = self.maskAccount(acc)
            } else {
                self.alipayMaskedAccount = "已绑定"
            }
        }
        popup.present(in: view)
    }
    
    @objc private func wechatTapped() {
        // 弹出微信实名认证弹窗
        let popup = WithdrawalRealnamePopupView(channel: .wechat)
        popup.onConfirm = { [weak self] input in
            guard let self = self else { return }
            self.wechatBound = true
            // 微信无账号输入，仅显示“已绑定”
            self.wechatMaskedAccount = "已绑定"
        }
        popup.present(in: view)
    }
    
    @objc private func unionPayTapped() {
        // TODO: 银联绑卡模块，稍后实现
        print("银联卡片被点击，待实现绑卡功能")
    }
}

// MARK: - 账户卡片视图
fileprivate class WithdrawalAccountCardView: UIControl {
    private let container = UIView()
    private let iconContainer = UIView()
    private let iconView = UIImageView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let arrowView = UIImageView(image: UIImage(systemName: "chevron.right"))
    
    var subtitle: String? {
        didSet { subtitleLabel.text = subtitle }
    }
    
    init(iconImageName: String, title: String) {
        super.init(frame: .zero)
        setupUI()
        iconContainer.backgroundColor = .clear
        iconView.image = UIImage(named: iconImageName)
        titleLabel.text = title
        subtitleLabel.text = "未绑定"
        accessibilityLabel = title
    }
    
    required init?(coder: NSCoder) { super.init(coder: coder); setupUI() }
    
    private func setupUI() {
        backgroundColor = .clear
        addSubview(container)
        container.backgroundColor = .white
        container.layer.cornerRadius = 12
        container.layer.masksToBounds = false
        container.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        container.layer.shadowOpacity = 1
        container.layer.shadowRadius = 6
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(80)
        }
        
        container.addSubview(iconContainer)
        iconContainer.layer.cornerRadius = 0
        iconContainer.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.size.equalTo(40)
        }
        
        iconContainer.addSubview(iconView)
        iconView.contentMode = .scaleAspectFit
        iconView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(40)
        }
        
        container.addSubview(arrowView)
        arrowView.tintColor = UIColor(hex: "#CCCCCC")
        arrowView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-14)
            make.centerY.equalToSuperview()
            make.width.equalTo(8)
            make.height.equalTo(14)
        }
        
        container.addSubview(titleLabel)
        titleLabel.textColor = .appTitleText
        titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(14)
            make.left.equalTo(iconContainer.snp.right).offset(12)
            make.right.lessThanOrEqualTo(arrowView.snp.left).offset(-10)
        }
        
        container.addSubview(subtitleLabel)
        subtitleLabel.textColor = UIColor(hex: "#888888")
        subtitleLabel.font = UIFont.systemFont(ofSize: 13)
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.left.equalTo(titleLabel)
            make.bottom.equalToSuperview().inset(14)
            make.right.lessThanOrEqualTo(arrowView.snp.left).offset(-10)
        }
    }
}
