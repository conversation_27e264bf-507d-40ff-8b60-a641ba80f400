//
//  GoldCountdownManager.swift
//  Shu<PERSON><PERSON>qi
//
//  全局金币倒计时管理器：使用本地持久化(endTimestamp, rewardCoins)，
//  通过 NotificationCenter 广播 Tick/Finished，确保离开页面/后台也能正确恢复状态。
//

import Foundation
import UIKit

extension Notification.Name {
    static let goldCountdownTick = Notification.Name("GoldCountdownTickNotification")
    static let goldCountdownFinished = Notification.Name("GoldCountdownFinishedNotification")
}

final class GoldCountdownManager {
    static let shared = GoldCountdownManager()

    private init() {
        loadState()
        resetDailyIfNeeded()
        configureAppLifecycle()
        if isRunning {
            startTimer()
            postTick()
        }
    }

    // MARK: - Public State
    private(set) var rewardCoins: Int = 0
    private(set) var dailyClaimCount: Int = 0
    let dailyLimit: Int = 3

    var isRunning: Bool {
        guard let end = endTimestamp else { return false }
        return Date() < end
    }

    var remainingSeconds: Int {
        guard let end = endTimestamp else { return 0 }
        let diff = Int(end.timeIntervalSinceNow)
        return max(0, diff)
    }

    // MARK: - Private State
    private var endTimestamp: Date? {
        didSet { persistState() }
    }
    private var timer: Timer?
    private var lastClaimDayKey: String = ""

    // MARK: - Public API
    func start(duration: TimeInterval, reward: Int) {
        guard duration > 0 else { return }
        // 达到每日上限则不再启动倒计时
        if isDailyLimitReached { return }
        rewardCoins = reward
        endTimestamp = Date().addingTimeInterval(duration)
        startTimer()
        postTick()
    }

    func clear() {
        stopTimer()
        rewardCoins = 0
        endTimestamp = nil
    }

    // MARK: - Timer
    private func startTimer() {
        stopTimer()
        // 在前台时每秒触发一次
        let t = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.handleTick()
        }
        RunLoop.main.add(t, forMode: .common)
        timer = t
    }

    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func handleTick() {
        if remainingSeconds <= 0 {
            stopTimer()
            endTimestamp = nil
            NotificationCenter.default.post(name: .goldCountdownFinished, object: nil, userInfo: ["reward": rewardCoins])
        } else {
            postTick()
        }
    }

    private func postTick() {
        NotificationCenter.default.post(name: .goldCountdownTick, object: nil, userInfo: ["remaining": remainingSeconds, "reward": rewardCoins])
    }

    // MARK: - Persist
    private let udKeyEndTime = "gold_countdown_end_time"
    private let udKeyReward = "gold_countdown_reward"
    private let udKeyDailyCount = "gold_daily_claim_count"
    private let udKeyLastDay = "gold_daily_last_day"

    private func persistState() {
        let ud = UserDefaults.standard
        if let end = endTimestamp {
            ud.set(end.timeIntervalSince1970, forKey: udKeyEndTime)
            ud.set(rewardCoins, forKey: udKeyReward)
        } else {
            ud.removeObject(forKey: udKeyEndTime)
            ud.set(rewardCoins, forKey: udKeyReward)
        }
        ud.set(dailyClaimCount, forKey: udKeyDailyCount)
        ud.set(lastClaimDayKey, forKey: udKeyLastDay)
        ud.synchronize()
    }

    private func loadState() {
        let ud = UserDefaults.standard
        rewardCoins = ud.integer(forKey: udKeyReward)
        let ts = ud.double(forKey: udKeyEndTime)
        if ts > 0 {
            let end = Date(timeIntervalSince1970: ts)
            if Date() < end {
                endTimestamp = end
            } else {
                endTimestamp = nil
            }
        } else {
            endTimestamp = nil
        }
        dailyClaimCount = ud.integer(forKey: udKeyDailyCount)
        lastClaimDayKey = ud.string(forKey: udKeyLastDay) ?? ""
    }

    // MARK: - App Lifecycle
    private func configureAppLifecycle() {
        NotificationCenter.default.addObserver(self, selector: #selector(appDidBecomeActive), name: UIApplication.didBecomeActiveNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(appDidEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
    }

    @objc private func appDidBecomeActive() {
        resetDailyIfNeeded()
        if isRunning { startTimer(); postTick() }
        else if endTimestamp != nil { // 结束于后台
            endTimestamp = nil
            NotificationCenter.default.post(name: .goldCountdownFinished, object: nil, userInfo: ["reward": rewardCoins])
        }
    }

    @objc private func appDidEnterBackground() {
        // 后台不保留定时器，使用时间差恢复
        stopTimer()
    }

    // MARK: - Daily limit logic
    var isDailyLimitReached: Bool {
        resetDailyIfNeeded()
        return dailyClaimCount >= dailyLimit
    }

    @discardableResult
    func incrementDailyClaim() -> Int {
        resetDailyIfNeeded()
        dailyClaimCount += 1
        persistState()
        return dailyClaimCount
    }

    func resetDailyIfNeeded() {
        let todayKey = Self.dayKey(for: Date())
        if lastClaimDayKey != todayKey {
            lastClaimDayKey = todayKey
            dailyClaimCount = 0
            persistState()
        }
    }

    private static func dayKey(for date: Date) -> String {
        let comp = Calendar.current.dateComponents([.year, .month, .day], from: date)
        let y = comp.year ?? 0
        let m = comp.month ?? 0
        let d = comp.day ?? 0
        return String(format: "%04d-%02d-%02d", y, m, d)
    }
}
