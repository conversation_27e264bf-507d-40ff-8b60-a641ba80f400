# 提现账号管理类型使用示例

## 概述
`GoldCoinSystemWithdrawalAccountManagementViewController` 已支持根据不同业务类型显示不同的UI界面。

## 类型定义
```swift
enum WithdrawalAccountManagementType {
    case goldCoin       // 金币系统
    case productShowcase // 商品橱窗（带货）
}
```

## 使用方法

### 1. 金币系统进入（默认）
```swift
// 方式一：使用默认类型
let vc = GoldCoinSystemWithdrawalAccountManagementViewController()
// 默认为 .goldCoin 类型，只显示支付宝和微信

// 方式二：显式指定类型
let vc = GoldCoinSystemWithdrawalAccountManagementViewController(type: .goldCoin)
```

### 2. 商品橱窗（带货）进入
```swift
let vc = GoldCoinSystemWithdrawalAccountManagementViewController(type: .productShowcase)
// 显示支付宝、微信和银联三个选项
```

## 功能差异

### 金币系统 (.goldCoin)
- ✅ 支付宝卡片
- ✅ 微信支付卡片
- ❌ 银联卡片（不显示）

### 商品橱窗 (.productShowcase)
- ✅ 支付宝卡片
- ✅ 微信支付卡片
- ✅ 银联卡片（显示，但点击事件待实现）

## 注意事项
1. 银联卡片的绑定功能需要单独的绑卡模块，目前只是占位实现
2. 银联卡片仅在商品橱窗类型时显示
3. 支付宝和微信的功能在两种类型下保持一致